/*
 Copyright 2012-2015, Yahoo Inc.
 Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.
 */
'use strict';

const percent = require('./percent');
const dataProperties = require('./data-properties');
const { CoverageSummary } = require('./coverage-summary');

// returns a data object that represents empty coverage
function emptyCoverage(filePath, reportLogic) {
    const cov = {
        path: filePath,
        statementMap: {},
        fnMap: {},
        branchMap: {},
        s: {},
        f: {},
        b: {}
    };
    if (reportLogic) cov.bT = {};
    return cov;
}

// asserts that a data object "looks like" a coverage object
function assertValidObject(obj) {
    const valid =
        obj &&
        obj.path &&
        obj.statementMap &&
        obj.fnMap &&
        obj.branchMap &&
        obj.s &&
        obj.f &&
        obj.b;
    if (!valid) {
        throw new Error(
            'Invalid file coverage object, missing keys, found:' +
                Object.keys(obj).join(',')
        );
    }
}

const keyFromLoc = ({ start, end }) =>
    `${start.line}|${start.column}|${end.line}|${end.column}`;

const isObj = o => !!o && typeof o === 'object';
const isLineCol = o =>
    isObj(o) && typeof o.line === 'number' && typeof o.column === 'number';
const isLoc = o => isObj(o) && isLineCol(o.start) && isLineCol(o.end);
const getLoc = o => (isLoc(o) ? o : isLoc(o.loc) ? o.loc : null);

// When merging, we can have a case where two ranges cover
// the same block of code with `hits=1`, and each carve out a
// different range with `hits=0` to indicate it's uncovered.
// Find the nearest container so that we can properly indicate
// that both sections are hit.
// Returns null if no containing item is found.
const findNearestContainer = (item, map) => {
    const itemLoc = getLoc(item);
    if (!itemLoc) return null;
    // the B item is not an identified range in the A set, BUT
    // it may be contained by an identified A range. If so, then
    // any hit of that containing A range counts as a hit of this
    // B range as well. We have to find the *narrowest* containing
    // range to be accurate, since ranges can be hit and un-hit
    // in a nested fashion.
    let nearestContainingItem = null;
    let containerDistance = null;
    let containerKey = null;
    for (const [i, mapItem] of Object.entries(map)) {
        const mapLoc = getLoc(mapItem);
        if (!mapLoc) continue;
        // contained if all of line distances are > 0
        // or line distance is 0 and col dist is >= 0
        const distance = [
            itemLoc.start.line - mapLoc.start.line,
            itemLoc.start.column - mapLoc.start.column,
            mapLoc.end.line - itemLoc.end.line,
            mapLoc.end.column - itemLoc.end.column
        ];
        if (
            distance[0] < 0 ||
            distance[2] < 0 ||
            (distance[0] === 0 && distance[1] < 0) ||
            (distance[2] === 0 && distance[3] < 0)
        ) {
            continue;
        }
        if (nearestContainingItem === null) {
            containerDistance = distance;
            nearestContainingItem = mapItem;
            containerKey = i;
            continue;
        }
        // closer line more relevant than closer column
        const closerBefore =
            distance[0] < containerDistance[0] ||
            (distance[0] === 0 && distance[1] < containerDistance[1]);
        const closerAfter =
            distance[2] < containerDistance[2] ||
            (distance[2] === 0 && distance[3] < containerDistance[3]);
        if (closerBefore || closerAfter) {
            // closer
            containerDistance = distance;
            nearestContainingItem = mapItem;
            containerKey = i;
        }
    }
    return containerKey;
};

// either add two numbers, or all matching entries in a number[]
const addHits = (aHits, bHits) => {
    if (typeof aHits === 'number' && typeof bHits === 'number') {
        return aHits + bHits;
    } else if (Array.isArray(aHits) && Array.isArray(bHits)) {
        return aHits.map((a, i) => (a || 0) + (bHits[i] || 0));
    }
    return null;
};

const addNearestContainerHits = (item, itemHits, map, mapHits) => {
    const container = findNearestContainer(item, map);
    if (container) {
        return addHits(itemHits, mapHits[container]);
    } else {
        return itemHits;
    }
};

const mergeProp = (aHits, aMap, bHits, bMap, itemKey = keyFromLoc) => {
    const aItems = {};
    for (const [key, itemHits] of Object.entries(aHits)) {
        const item = aMap[key];
        aItems[itemKey(item)] = [itemHits, item];
    }
    const bItems = {};
    for (const [key, itemHits] of Object.entries(bHits)) {
        const item = bMap[key];
        bItems[itemKey(item)] = [itemHits, item];
    }
    const mergedItems = {};
    for (const [key, aValue] of Object.entries(aItems)) {
        let aItemHits = aValue[0];
        const aItem = aValue[1];
        const bValue = bItems[key];
        if (!bValue) {
            // not an identified range in b, but might be contained by one
            aItemHits = addNearestContainerHits(aItem, aItemHits, bMap, bHits);
        } else {
            // is an identified range in b, so add the hits together
            aItemHits = addHits(aItemHits, bValue[0]);
        }
        mergedItems[key] = [aItemHits, aItem];
    }
    // now find the items in b that are not in a. already added matches.
    for (const [key, bValue] of Object.entries(bItems)) {
        let bItemHits = bValue[0];
        const bItem = bValue[1];
        if (mergedItems[key]) continue;
        // not an identified range in b, but might be contained by one
        bItemHits = addNearestContainerHits(bItem, bItemHits, aMap, aHits);
        mergedItems[key] = [bItemHits, bItem];
    }

    const hits = {};
    const map = {};

    Object.values(mergedItems).forEach(([itemHits, item], i) => {
        hits[i] = itemHits;
        map[i] = item;
    });

    return [hits, map];
};

/**
 * provides a read-only view of coverage for a single file.
 * The deep structure of this object is documented elsewhere. It has the following
 * properties:
 *
 * * `path` - the file path for which coverage is being tracked
 * * `statementMap` - map of statement locations keyed by statement index
 * * `fnMap` - map of function metadata keyed by function index
 * * `branchMap` - map of branch metadata keyed by branch index
 * * `s` - hit counts for statements
 * * `f` - hit count for functions
 * * `b` - hit count for branches
 */
class FileCoverage {
    /**
     * @constructor
     * @param {Object|FileCoverage|String} pathOrObj is a string that initializes
     * and empty coverage object with the specified file path or a data object that
     * has all the required properties for a file coverage object.
     */
    constructor(pathOrObj, reportLogic = false) {
        if (!pathOrObj) {
            throw new Error(
                'Coverage must be initialized with a path or an object'
            );
        }
        if (typeof pathOrObj === 'string') {
            this.data = emptyCoverage(pathOrObj, reportLogic);
        } else if (pathOrObj instanceof FileCoverage) {
            this.data = pathOrObj.data;
        } else if (typeof pathOrObj === 'object') {
            this.data = pathOrObj;
        } else {
            throw new Error('Invalid argument to coverage constructor');
        }
        assertValidObject(this.data);
    }

    /**
     * returns computed line coverage from statement coverage.
     * This is a map of hits keyed by line number in the source.
     */
    getLineCoverage() {
        const statementMap = this.data.statementMap;
        const statements = this.data.s;
        const lineMap = Object.create(null);

        Object.entries(statements).forEach(([st, count]) => {
            /* istanbul ignore if: is this even possible? */
            if (!statementMap[st]) {
                return;
            }
            const { line } = statementMap[st].start;
            const prevVal = lineMap[line];
            if (prevVal === undefined || prevVal < count) {
                lineMap[line] = count;
            }
        });
        return lineMap;
    }

    /**
     * returns an array of uncovered line numbers.
     * @returns {Array} an array of line numbers for which no hits have been
     *  collected.
     */
    getUncoveredLines() {
        const lc = this.getLineCoverage();
        const ret = [];
        Object.entries(lc).forEach(([l, hits]) => {
            if (hits === 0) {
                ret.push(l);
            }
        });
        return ret;
    }

    /**
     * returns a map of branch coverage by source line number.
     * @returns {Object} an object keyed by line number. Each object
     * has a `covered`, `total` and `coverage` (percentage) property.
     */
    getBranchCoverageByLine() {
        const branchMap = this.branchMap;
        const branches = this.b;
        const ret = {};
        Object.entries(branchMap).forEach(([k, map]) => {
            const line = map.line || map.loc.start.line;
            const branchData = branches[k];
            ret[line] = ret[line] || [];
            ret[line].push(...branchData);
        });
        Object.entries(ret).forEach(([k, dataArray]) => {
            const covered = dataArray.filter(item => item > 0);
            const coverage = (covered.length / dataArray.length) * 100;
            ret[k] = {
                covered: covered.length,
                total: dataArray.length,
                coverage
            };
        });
        return ret;
    }

    /**
     * return a JSON-serializable POJO for this file coverage object
     */
    toJSON() {
        return this.data;
    }

    /**
     * merges a second coverage object into this one, updating hit counts
     * @param {FileCoverage} other - the coverage object to be merged into this one.
     *  Note that the other object should have the same structure as this one (same file).
     */
    merge(other) {
        if (other.all === true) {
            return;
        }

        if (this.all === true) {
            this.data = other.data;
            return;
        }

        let [hits, map] = mergeProp(
            this.s,
            this.statementMap,
            other.s,
            other.statementMap
        );
        this.data.s = hits;
        this.data.statementMap = map;

        const keyFromLocProp = x => keyFromLoc(x.loc);
        const keyFromLocationsProp = x => keyFromLoc(x.locations[0]);

        [hits, map] = mergeProp(
            this.f,
            this.fnMap,
            other.f,
            other.fnMap,
            keyFromLocProp
        );
        this.data.f = hits;
        this.data.fnMap = map;

        [hits, map] = mergeProp(
            this.b,
            this.branchMap,
            other.b,
            other.branchMap,
            keyFromLocationsProp
        );
        this.data.b = hits;
        this.data.branchMap = map;

        // Tracking additional information about branch truthiness
        // can be optionally enabled:
        if (this.bT && other.bT) {
            [hits, map] = mergeProp(
                this.bT,
                this.branchMap,
                other.bT,
                other.branchMap,
                keyFromLocationsProp
            );
            this.data.bT = hits;
        }
    }

    computeSimpleTotals(property) {
        let stats = this[property];

        if (typeof stats === 'function') {
            stats = stats.call(this);
        }

        const ret = {
            total: Object.keys(stats).length,
            covered: Object.values(stats).filter(v => !!v).length,
            skipped: 0
        };
        ret.pct = percent(ret.covered, ret.total);
        return ret;
    }

    computeBranchTotals(property) {
        const stats = this[property];
        const ret = { total: 0, covered: 0, skipped: 0 };

        Object.values(stats).forEach(branches => {
            ret.covered += branches.filter(hits => hits > 0).length;
            ret.total += branches.length;
        });
        ret.pct = percent(ret.covered, ret.total);
        return ret;
    }

    /**
     * 计算新增代码覆盖率 - 使用分支覆盖率逻辑
     * @returns {Object} 新增代码覆盖率统计
     */
    computeIncrementalTotals() {
        // 验证新增代码的有效性
        if (!this.data || !this.data.lines || this.data.lines.length === 0) {
            return { total: 0, covered: 0, skipped: 0, pct: 0 };
        }

        const incrementLines = this.data.lines || [];
        const ret = { total: 0, covered: 0, skipped: 0 };

        // 判断文件是否被执行过（检查是否有任何覆盖率数据）
        const isFileExecuted = this._isFileExecuted();

        // 调试信息
        console.log(`[DEBUG] computeIncrementalTotals for file: ${this.path}`);
        console.log(`[DEBUG] incrementLines:`, incrementLines);
        console.log(`[DEBUG] isFileExecuted:`, isFileExecuted);

        // 获取源代码来检查是否是标签代码
        const fs = require('fs');
        let sourceLines = [];
        try {
            if (this.path) {
                const sourceText = fs.readFileSync(this.path, 'utf8');
                sourceLines = sourceText.split(/\r?\n/);
            }
        } catch (e) {
            // Cannot read source file
        }

        // 检查文件的整体分支覆盖率，如果为0，需要特殊处理标签代码
        const branchTotals = this.computeBranchTotals('b');
        if (branchTotals.total > 0 && branchTotals.covered === 0) {
            // 即使分支覆盖率为0，也要检查新增行中的标签代码
            incrementLines.forEach(lineNum => {
                ret.total++;
                const lineContent = sourceLines[lineNum - 1] || '';
                const isTag = this._isTemplateOrTagCode(lineContent.trim());
                const isVueComponent = this._isVueComponentLevelCode(lineContent.trim());

                console.log(`[DEBUG] Line ${lineNum}: "${lineContent.trim()}", isTag: ${isTag}, isVueComponent: ${isVueComponent}, isFileExecuted: ${isFileExecuted}`);

                // 如果是标签代码且文件被执行了，则认为标签代码被覆盖
                if ((isTag || isVueComponent) && isFileExecuted) {
                    ret.covered++;
                    console.log(`[DEBUG] Line ${lineNum} marked as covered (tag code)`);
                }
            });
            ret.pct = percent(ret.covered, ret.total);
            return ret;
        }

        // 使用分支覆盖率数据而不是行覆盖率数据，与分支覆盖率保持一致
        const branchStats = this.b;
        const branchMeta = this.branchMap;

        // 记录已处理的行，避免重复计算
        const processedLines = new Set();

        // 遍历所有分支，找出在新增行范围内的分支
        if (branchStats && branchMeta) {
            Object.entries(branchStats).forEach(([branchName, branchArray]) => {
                const meta = branchMeta[branchName];
                if (!meta || !meta.locations) {
                    return;
                }

                // 检查分支是否在新增行范围内
                const branchInIncrementLines = meta.locations.some(location => {
                    if (!location.start) return false;
                    const branchLine = location.start.line;
                    return incrementLines.includes(branchLine);
                });

                if (branchInIncrementLines) {
                    // 标记这些行已被处理
                    meta.locations.forEach(location => {
                        if (location.start && incrementLines.includes(location.start.line)) {
                            processedLines.add(location.start.line);
                        }
                    });

                    // 计算这个分支的覆盖情况（与分支覆盖率计算逻辑一致）
                    branchArray.forEach(hits => {
                        ret.total++;
                        if (hits > 0) {
                            ret.covered++;
                        }
                    });
                }
            });

            // 处理没有分支映射的新增行（通常是标签代码）
            incrementLines.forEach(lineNum => {
                if (!processedLines.has(lineNum)) {
                    ret.total++;
                    const lineContent = sourceLines[lineNum - 1] || '';
                    const isTag = this._isTemplateOrTagCode(lineContent.trim());
                    const isVueComponent = this._isVueComponentLevelCode(lineContent.trim());

                    console.log(`[DEBUG] Branch section - Line ${lineNum}: "${lineContent.trim()}", isTag: ${isTag}, isVueComponent: ${isVueComponent}, isFileExecuted: ${isFileExecuted}`);

                    // 如果是标签代码且文件被执行了，则认为标签代码被覆盖
                    if ((isTag || isVueComponent) && isFileExecuted) {
                        ret.covered++;
                        console.log(`[DEBUG] Branch section - Line ${lineNum} marked as covered (tag code)`);
                    }
                }
            });
        }

        // 如果没有找到任何分支，使用语句覆盖率数据进行计算（与分支覆盖率逻辑保持一致）
        if (ret.total === 0) {
            const statementStats = this.s;
            const statementMeta = this.statementMap;

            // 记录已处理的行，避免重复计算
            const processedLines = new Set();

            if (statementStats && statementMeta) {
                Object.entries(statementStats).forEach(([stName, count]) => {
                    const meta = statementMeta[stName];
                    if (meta.skip) {
                        return;
                    }

                    // 检查语句是否在新增行范围内
                    const statementStartLine = meta.start.line;
                    const statementEndLine = meta.end.line;

                    // 判断语句是否与新增行有交集
                    const isInIncrementLines = incrementLines.some(lineNum =>
                        lineNum >= statementStartLine && lineNum <= statementEndLine
                    );

                    if (isInIncrementLines) {
                        ret.total++;
                        // 标记这些行已被处理
                        for (let line = statementStartLine; line <= statementEndLine; line++) {
                            if (incrementLines.includes(line)) {
                                processedLines.add(line);
                            }
                        }

                        if (count > 0) {
                            ret.covered++;
                        } else {
                            // 语句未被执行，检查是否是标签代码
                            const lineContent = sourceLines[statementStartLine - 1] || '';
                            const isTag = this._isTemplateOrTagCode(lineContent.trim());
                            const isVueComponent = this._isVueComponentLevelCode(lineContent.trim());

                            console.log(`[DEBUG] Statement section - Line ${statementStartLine}: "${lineContent.trim()}", isTag: ${isTag}, isVueComponent: ${isVueComponent}, isFileExecuted: ${isFileExecuted}`);

                            // 如果是标签代码且文件被执行了，则认为标签代码被覆盖
                            if ((isTag || isVueComponent) && isFileExecuted) {
                                ret.covered++;
                                console.log(`[DEBUG] Statement section - Line ${statementStartLine} marked as covered (tag code)`);
                            }
                        }
                    }
                });
            }

            // 处理没有语句映射的新增行（通常是标签代码）
            incrementLines.forEach(lineNum => {
                if (!processedLines.has(lineNum)) {
                    ret.total++;
                    const lineContent = sourceLines[lineNum - 1] || '';
                    const isTag = this._isTemplateOrTagCode(lineContent.trim());
                    const isVueComponent = this._isVueComponentLevelCode(lineContent.trim());

                    console.log(`[DEBUG] Statement fallback section - Line ${lineNum}: "${lineContent.trim()}", isTag: ${isTag}, isVueComponent: ${isVueComponent}, isFileExecuted: ${isFileExecuted}`);

                    // 如果是标签代码且文件被执行了，则认为标签代码被覆盖
                    if ((isTag || isVueComponent) && isFileExecuted) {
                        ret.covered++;
                        console.log(`[DEBUG] Statement fallback section - Line ${lineNum} marked as covered (tag code)`);
                    }
                }
            });
        }

        ret.pct = percent(ret.covered, ret.total);
        console.log(`[DEBUG] Final result for ${this.path}:`, ret);
        return ret;
    }

    /**
     * 判断文件是否被执行过
     * @returns {boolean} 文件是否被执行
     */
    _isFileExecuted() {
        // 检查是否有任何语句被执行
        const statementStats = this.s;
        if (statementStats) {
            const hasExecutedStatement = Object.values(statementStats).some(count => count > 0);
            if (hasExecutedStatement) {
                return true;
            }
        }

        // 检查是否有任何函数被执行
        const functionStats = this.f;
        if (functionStats) {
            const hasExecutedFunction = Object.values(functionStats).some(count => count > 0);
            if (hasExecutedFunction) {
                return true;
            }
        }

        // 检查是否有任何分支被执行
        const branchStats = this.b;
        if (branchStats) {
            const hasExecutedBranch = Object.values(branchStats).some(branchArray =>
                branchArray.some(count => count > 0)
            );
            if (hasExecutedBranch) {
                return true;
            }
        }

        return false;
    }

    /**
     * 判断是否是模板或标签代码
     * @param {string} lineContent 行内容
     * @returns {boolean} 是否是标签代码
     */
    _isTemplateOrTagCode(lineContent) {
        const trimmed = lineContent.trim();

        // HTML/XML 标签
        if (trimmed.startsWith('<') && (trimmed.endsWith('>') || trimmed.endsWith('/>'))) {
            return true;
        }

        // Vue 模板语法
        if (trimmed.includes('v-if=') || trimmed.includes('v-for=') ||
            trimmed.includes('v-show=') || trimmed.includes('v-model=') ||
            trimmed.includes(':key=') || trimmed.includes('@click=') ||
            trimmed.includes('{{') || trimmed.includes('}}')) {
            return true;
        }

        // React JSX 语法
        if (trimmed.includes('className=') || trimmed.includes('onClick=') ||
            trimmed.includes('onChange=') || trimmed.includes('onSubmit=')) {
            return true;
        }

        // 模板字符串中的HTML
        if (trimmed.includes('`') && (trimmed.includes('<') || trimmed.includes('>'))) {
            return true;
        }

        // CSS 样式 - 更精确的匹配
        if ((trimmed.includes('style="') || trimmed.includes("style='")) ||
            (trimmed.includes('class="') || trimmed.includes("class='"))) {
            return true;
        }

        return false;
    }

    /**
     * 判断是否是Vue组件级别的代码
     * @param {string} lineContent 行内容
     * @returns {boolean} 是否是Vue组件代码
     */
    _isVueComponentLevelCode(lineContent) {
        const trimmed = lineContent.trim();

        // 只处理Vue文件
        if (!this.path || !this.path.endsWith('.vue')) {
            return false;
        }

        // 1. 所有import语句（模块加载时执行）
        if (trimmed.startsWith('import ')) {
            return true;
        }

        // 2. 组件定义和配置（组件实例化时执行）
        if (trimmed.includes('export default') ||
            trimmed.includes('defineComponent') ||
            trimmed.match(/^\s*name\s*:/) ||
            trimmed.match(/^\s*components\s*:/) ||
            trimmed.match(/^\s*props\s*:/) ||
            trimmed.match(/^\s*emits\s*:/)) {
            return true;
        }

        // 3. 组件注册（在components对象中的组件名）
        if (trimmed.match(/^[A-Z][a-zA-Z0-9]*\s*,?\s*$/) ||
            trimmed.match(/^[A-Z][a-zA-Z0-9]*\s*:\s*[A-Z][a-zA-Z0-9]*\s*,?\s*$/)) {
            return true;
        }

        // 4. Vue 3 Composition API（setup时执行）
        if (trimmed.includes('defineProps') ||
            trimmed.includes('defineEmits') ||
            trimmed.includes('defineExpose') ||
            trimmed.includes('defineSlots')) {
            return true;
        }

        // 5. 生命周期钩子定义（组件生命周期时自动调用）
        if (trimmed.match(/^\s*(created|mounted|beforeMount|beforeUpdate|updated|beforeUnmount|unmounted)\s*\(/)) {
            return true;
        }

        // 6. Vue文件的结构标签
        if (trimmed.startsWith('<template') ||
            trimmed.startsWith('<style') ||
            trimmed.startsWith('<script')) {
            return true;
        }

        return false;
    }

    /**
     * resets hit counts for all statements, functions and branches
     * in this coverage object resulting in zero coverage.
     */
    resetHits() {
        const statements = this.s;
        const functions = this.f;
        const branches = this.b;
        const branchesTrue = this.bT;
        Object.keys(statements).forEach(s => {
            statements[s] = 0;
        });
        Object.keys(functions).forEach(f => {
            functions[f] = 0;
        });
        Object.keys(branches).forEach(b => {
            branches[b].fill(0);
        });
        // Tracking additional information about branch truthiness
        // can be optionally enabled:
        if (branchesTrue) {
            Object.keys(branchesTrue).forEach(bT => {
                branchesTrue[bT].fill(0);
            });
        }
    }

    /**
     * returns a CoverageSummary for this file coverage object
     * @returns {CoverageSummary}
     */
    toSummary() {
        const ret = {};
        ret.lines = this.computeSimpleTotals('getLineCoverage');
        ret.functions = this.computeSimpleTotals('f', 'fnMap');
        ret.statements = this.computeSimpleTotals('s', 'statementMap');
        ret.branches = this.computeBranchTotals('b');
        // Tracking additional information about branch truthiness
        // can be optionally enabled:
        if (this.bT) {
            ret.branchesTrue = this.computeBranchTotals('bT');
        }
        // 计算新增代码覆盖率
        ret.incremental = this.computeIncrementalTotals();
        return new CoverageSummary(ret);
    }
}

// expose coverage data attributes
dataProperties(FileCoverage, [
    'path',
    'statementMap',
    'fnMap',
    'branchMap',
    's',
    'f',
    'b',
    'bT',
    'all'
]);

module.exports = {
    FileCoverage,
    // exported for testing
    findNearestContainer,
    addHits,
    addNearestContainerHits
};
